// Load polyfills first, before any other imports
import './polyfills';

import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import ErrorBoundary from './components/ErrorBoundary.tsx';
import './index.css';

// Initialize Firebase (this will run the code in firebase/config.ts)
console.log('🔥 Initializing Firebase...');
import './firebase/config';

// Initialize console filtering to reduce third-party noise
import { initializeConsoleFiltering, filterStripeErrors } from './utils/consoleFilter';
initializeConsoleFiltering();
filterStripeErrors();

// Initialize Sentry for error tracking and performance monitoring
// import { initSentry } from './utils/sentry';
// import { initializeSecurity } from './utils/security';
// import { setupGlobalErrorHandling } from './utils/errorHandler';
// import { initPerformanceMonitoring } from './utils/performance';
// import { initializeMonitoring } from './utils/monitoring';


// Initialize security measures
// initializeSecurity();

// Initialize global error handling
// setupGlobalErrorHandling();

// Initialize Sentry
// initSentry();

// Initialize performance monitoring
// initPerformanceMonitoring();

// Initialize comprehensive monitoring and alerting
// initializeMonitoring({
//   environment: import.meta.env.MODE || 'development',
//   sampleRate: import.meta.env.PROD ? 0.1 : 1.0,
//   enableErrorTracking: true,
//   enablePerformanceTracking: true,
//   enableUserTracking: import.meta.env.PROD,
//   enableAPIMonitoring: true,
//   enableCustomMetrics: true,
// });

// Clear any existing service workers in development
if (import.meta.env.DEV && 'serviceWorker' in navigator) {
  navigator.serviceWorker.getRegistrations().then(function(registrations) {
    for(const registration of registrations) {
      registration.unregister();
    }
  });
}

// Service worker registration is now handled by vite-plugin-pwa
// The plugin will automatically register the service worker in production

console.log('🚀 Starting React app...');
console.log('📊 Environment variables check:', {
  NODE_ENV: import.meta.env.MODE,
  FIREBASE_API_KEY: import.meta.env.VITE_FIREBASE_API_KEY ? '✅ Present' : '❌ Missing',
  FIREBASE_PROJECT_ID: import.meta.env.VITE_FIREBASE_PROJECT_ID ? '✅ Present' : '❌ Missing',
  STRIPE_KEY: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY ? '✅ Present' : '❌ Missing'
});

// Polyfills are now loaded from ./polyfills.ts

const rootElement = document.getElementById('root');
if (!rootElement) {
  console.error('❌ Root element not found!');
  throw new Error('Root element not found');
}

console.log('✅ Root element found, creating React root...');

// Hide the fallback loading screen
const fallback = document.getElementById('loading-fallback');
if (fallback) {
  fallback.style.display = 'none';
}

try {
  const root = createRoot(rootElement);
  root.render(
    <StrictMode>
      <ErrorBoundary>
        <App />
      </ErrorBoundary>
    </StrictMode>
  );
  console.log('✅ React app rendered successfully');
} catch (error) {
  console.error('❌ React app render failed:', error);
  // Show error in fallback if React fails
  if (fallback) {
    fallback.style.display = 'flex';
    fallback.innerHTML = `
      <div style="
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 40px;
        max-width: 500px;
        width: 100%;
        text-align: center;
      ">
        <h1 style="font-size: 2.5rem; margin-bottom: 1rem; font-weight: 700; color: white;">
          🐝 Hive Campus
        </h1>
        <p style="font-size: 1.2rem; margin-bottom: 2rem; color: #ffeb3b;">
          ⚠️ Application Error
        </p>
        <p style="font-size: 1rem; margin-bottom: 2rem; color: white; opacity: 0.9;">
          ${error.message || 'An error occurred while loading the application'}
        </p>
        <button onclick="window.location.reload()" style="
          background: #4CAF50;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 1rem;
          cursor: pointer;
        ">
          🔄 Refresh Page
        </button>
      </div>
    `;
  }
}
